import { Page } from '@playwright/test';
import logger from '../utils/logger';
import { isDataView } from 'util/types';

export class DailyTasksPage {
  private page: Page;
  private tasksList = '//div[@class="ant-card ant-card-bordered h-min sticky top-4 fle-grow-1 css-sokenu"]//div[@class="ant-card-body"]';//div[@class='ant-card-body"]';
  private pageTitle = '//span[@class="ant-typography text-2xl font-bold css-sokenu"]'; // Update with actual selector
  private dailyTaskTab = '//a[@class="no-underline rounded-md flex items-center p-2 transition-all duration-200 bg-[#fff4e6] text-[#f76b15]"]'; 

  constructor(page: Page) {
    this.page = page;
  }

  async isPageLoaded(): Promise<boolean> {
    logger.info('Checking if Daily Tasks page is loaded');
    await this.page.waitForSelector(this.pageTitle);
    return await this.page.isVisible(this.pageTitle);
  }

  async getPageTitle(): Promise<string> {
    const title = await this.page.textContent(this.pageTitle);
    logger.info(`Daily Tasks page title: ${title}`);
    return title || '';
  }

  async isTasksListVisible(): Promise<boolean> {
    logger.info('Checking if tasks list is visible');
    return await this.page.isVisible(this.tasksList);
  }

  async isDataViewVisible(): Promise<boolean> {
    logger.info('Checking if data view is visible');
    return await this.page.isVisible(this.dailyTaskTab);
  }
}