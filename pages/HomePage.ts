import { Page } from '@playwright/test';
import logger from '../utils/logger';
import { getAppBaseUrl } from '../utils/environment';

export class HomePage {
    private page: Page;
    private homeLogo = '//a[@class="active"]'; // Cậ<PERSON> nhật selector thực tế
    private portalIcon = '//div[contains(@class,"ant-row host-justify-center css-dev-only-do-not-override-sjnkpu")]//div[1]//a[1]//div[1]//div[1]//div[1]//span[1]//*[name()="svg"]//*[name()="path" and contains(@d,"M9.0023 4.")]';


    constructor(page: Page) {
        this.page = page;
    }

    async navigate() {
        const baseUrl = getAppBaseUrl();
        logger.info(`Navigating to Herond login page: ${baseUrl}/login`);
        await this.page.goto(`${baseUrl}/login`);
    }

    async verifyHomeLogoIsVisible() {
        logger.info('Verifying that home logo is visible on the homepage');
        await this.page.waitForSelector(this.homeLogo, { state: 'visible', timeout: 5000 });
    }
    

    async clickPortalIcon() {
        logger.info('Clicking on portal icon');
        await this.page.waitForSelector(this.portalIcon, { state: 'visible' });
        await this.page.click(this.portalIcon);
    }
}
