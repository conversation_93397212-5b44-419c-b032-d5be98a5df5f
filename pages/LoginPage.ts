import { Page } from '@playwright/test';
import logger from '../utils/logger';
import { getAppBaseUrl } from '../utils/environment';

export class LoginPage {
  private page: Page;
  private emailInput = '//input[@id="login_username"]';
  private passwordInput = '//input[@id="login_password"]';
  private loginButton =  '//button[@type="submit"]';
  private errorMessage = '//div[@role="status"]'; // Cập nhật selector thực tế

  constructor(page: Page) {
    this.page = page;
  }

  async navigate() {
    const baseUrl = getAppBaseUrl();
    logger.info(`Navigating to Herond login page: ${baseUrl}/login`);
    await this.page.goto(`${baseUrl}/login`);
  }

  async enterUsername(username: string) {
    logger.info(`Entering email: ${username}`);
    await this.page.fill(this.emailInput, username);
  }

  async enterPassword(password: string) {
    logger.info(`Entering password: ${password}`);
    await this.page.fill(this.passwordInput, password);
  }

  async clickLogin() {
    logger.info('Clicking login button');
    await this.page.click(this.loginButton);
  }

  async getErrorMessage(): Promise<string> {
    const message = await this.page.textContent(this.errorMessage);
    return message || '';
  }
}
