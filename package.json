{"name": "playwright-automation", "version": "1.0.0", "description": "Automation framework using Playwright and TypeScript", "type": "module", "scripts": {"build": "tsc --noEmit", "test": "rm -rf allure-results && ALLURE_RESULTS_DIR=\"$(pwd)/reports/allure-results\" playwright test", "test:dev": "APP_ENV=dev ALLURE_RESULTS_DIR=\"$(pwd)/reports/allure-results\" playwright test", "test:prod": "APP_ENV=production playwright test", "test:ci": "APP_ENV=dev playwright test", "test:ci-prod": "APP_ENV=production playwright test", "allure:generate": "allure generate reports/allure-results -o reports/allure-report --clean", "allure:open": "allure open reports/allure-report", "allure:serve": "allure serve reports/allure-results", "test:allure": "playwright test && allure generate reports/allure-results -o reports/allure-report --clean && allure open reports/allure-report", "test:report": "npm run test && npm run allure:serve", "html:open": "npx playwright show-report reports/html-report", "clean": "rm -rf reports"}, "devDependencies": {"@aws-sdk/client-secrets-manager": "^3.812.0", "@playwright/test": "^1.52.0", "@types/node": "^22.15.32", "allure-playwright": "^3.2.2", "typescript": "^5.6.2", "winston": "^3.14.2"}}