import { defineConfig, devices } from '@playwright/test';
import path from 'path';
import fs from 'node:fs';
import os from 'os';

// Thiết lập mặc định ENV nếu chưa có
process.env.ENV = process.env.ENV || 'rc';

// <PERSON><PERSON> báo đường dẫn thư mục báo cáo theo ENV
const reportsBaseDir = path.join('reports', process.env.ENV || 'default');
const allureResultsDir = process.env.ALLURE_RESULTS_DIR 
  ? path.resolve(process.env.ALLURE_RESULTS_DIR) 
  : path.join(reportsBaseDir, 'allure-results');
const htmlReportDir = path.join(reportsBaseDir, 'html-report');
const testResultsDir = path.join(reportsBaseDir, 'raw');
const jsonReportDir = path.join(reportsBaseDir, 'json-report');

// T<PERSON><PERSON> thư mục nếu chưa tồn tại
for (const dir of [allureResultsDir, htmlReportDir, testResultsDir, jsonReportDir]) {
  if (!fs.existsSync(dir)) {
    fs.mkdirSync(dir, { recursive: true });
  }
}

// Log thông tin đường dẫn cho debug
console.log('ENV:', process.env.ENV);
console.log('Allure Results Dir:', allureResultsDir);

export default defineConfig({
  testDir: './tests',
  fullyParallel: true,
  forbidOnly: !!process.env.CI,
  retries: process.env.CI ? 2 : 0,
  workers: process.env.CI ? 1 : undefined,
  outputDir: testResultsDir,
  reporter: [
    ['list'],
    ['html', { outputFolder: htmlReportDir }],
    ['json', { outputFile: path.join(jsonReportDir, 'test-results.json') }],
    ['allure-playwright', {
      detail: true,
      resultsDir: allureResultsDir,
      suiteTitle: false,
      environmentInfo: {
        ENV: process.env.ENV,
        USER: os.userInfo().username,
        NODE_VERSION: process.version,
        OS: os.platform(),
        FRAMEWORK: 'playwright'
      },
    }]
  ],
  use: {
    trace: 'on-first-retry',
    screenshot: 'only-on-failure',
    video: 'retain-on-failure',
  },
  projects: [
    {
      name: 'chromium',
      use: { ...devices['Desktop Chrome'] },
    },
  ],
});
