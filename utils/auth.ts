import { Page } from '@playwright/test';
import { getSecret } from './secretManager';
import logger from './logger';
import { getAppBaseUrl, getEnvironments } from './environment';
import * as fs from 'fs';
import * as path from 'path';
import * as crypto from 'crypto';

export class AuthManager {
  private static credentials: { [key: string]: { username: string, password: string } } = {};
  private static isEncrypted = false;
  private static credentialsPath = path.resolve(__dirname, '../tests/test-data/user-data/credentials.json');
  private static encryptionKey = process.env.ENCRYPTION_KEY || '';
  private static encryptionIV = process.env.ENCRYPTION_IV || '';
  
  // Mã hóa dữ liệu
  private static encrypt(text: string): string {
    if (!this.encryptionKey || this.encryptionKey.length < 32) return text;
    
    try {
      const cipher = crypto.createCipheriv(
        'aes-256-cbc', 
        Buffer.from(this.encryptionKey.slice(0, 32)), 
        Buffer.from(this.encryptionIV?.slice(0, 16) || '0000000000000000')
      );
      let encrypted = cipher.update(text, 'utf8', 'hex');
      encrypted += cipher.final('hex');
      return `enc:${encrypted}`;
    } catch (error) {
      logger.error('Mã hóa thất bại:', error);
      return text;
    }
  }
  
  // Giải mã dữ liệu
  private static decrypt(text: string): string {
    if (!text.startsWith('enc:') || !this.encryptionKey) return text;
    
    try {
      const encryptedData = text.substring(4);
      const decipher = crypto.createDecipheriv(
        'aes-256-cbc', 
        Buffer.from(this.encryptionKey.slice(0, 32)), 
        Buffer.from(this.encryptionIV?.slice(0, 16) || '0000000000000000')
      );
      let decrypted = decipher.update(encryptedData, 'hex', 'utf8');
      decrypted += decipher.final('utf8');
      return decrypted;
    } catch (error) {
      logger.error('Giải mã thất bại:', error);
      return text.substring(4);
    }
  }
  
  // Lấy thông tin đăng nhập từ nhiều nguồn
  private static async getCredentialsFromSources(type: string, env: string): Promise<{username: string, password: string}> {
    // 1. Thử lấy từ file JSON
    try {
      if (fs.existsSync(this.credentialsPath)) {
        const credData = JSON.parse(fs.readFileSync(this.credentialsPath, 'utf8'));
        if (type === 'valid' && credData[env]?.username && credData[env]?.password) {
          return credData[env];
        } else if (type === 'invalid' && credData.invalid?.username && credData.invalid?.password) {
          return credData.invalid;
        } else if (type === 'valid' && credData.default?.username && credData.default?.password) {
          logger.warn(`Sử dụng thông tin mặc định từ file cho môi trường ${env}`);
          return credData.default;
        }
      }
    } catch (e) {
      logger.error(`Lỗi đọc file credentials: ${e}`);
    }
    
    // 2. Thử lấy từ Secret Manager
    if (type === 'valid') {
      try {
        const { isRunningInProduction } = getEnvironments();
        if (isRunningInProduction) {
          const secretName = `herond-${env}-credentials`;
          const secret = await getSecret(secretName);
          if (secret) {
            const parsed = JSON.parse(secret);
            if (parsed.username && parsed.password) return parsed;
          }
        }
      } catch (e) {
        logger.error(`Lỗi lấy secret: ${e}`);
      }
    }
    
    // 3. Thử lấy từ biến môi trường
    const envPrefix = env.toUpperCase();
    if (type === 'valid') {
      const username = process.env[`${envPrefix}_HEROND_USERNAME`];
      const password = process.env[`${envPrefix}_HEROND_PASSWORD`];
      if (username && password) return { username, password };
      
      // Fallback to default env vars
      const defaultUser = process.env.DEFAULT_HEROND_USERNAME;
      const defaultPass = process.env.DEFAULT_HEROND_PASSWORD;
      if (defaultUser && defaultPass) {
        logger.warn(`Sử dụng thông tin mặc định từ biến môi trường`);
        return { username: defaultUser, password: defaultPass };
      }
    } else if (type === 'invalid') {
      const username = process.env.INVALID_HEROND_USERNAME;
      const password = process.env.INVALID_HEROND_PASSWORD;
      if (username && password) return { username, password };
    }
    
    // Không tìm thấy thông tin đăng nhập
    throw new Error(`Không tìm thấy thông tin đăng nhập ${type} cho môi trường ${env}`);
  }
  
  static async initialize() {
    if (Object.keys(this.credentials).length > 0) return;
    
    try {
      const { appEnv } = getEnvironments();
      
      // Lấy thông tin đăng nhập hợp lệ
      const validCreds = await this.getCredentialsFromSources('valid', appEnv);
      this.credentials.valid = {
        username: this.encrypt(validCreds.username),
        password: this.encrypt(validCreds.password)
      };
      
      // Lấy thông tin đăng nhập không hợp lệ
      try {
        const invalidCreds = await this.getCredentialsFromSources('invalid', appEnv);
        this.credentials.invalid = {
          username: this.encrypt(invalidCreds.username),
          password: this.encrypt(invalidCreds.password)
        };
      } catch (e) {
        logger.warn(`Không tìm thấy thông tin đăng nhập không hợp lệ: ${e}`);
        this.credentials.invalid = {
          username: this.encrypt('<EMAIL>'),
          password: this.encrypt('invalidPassword')
        };
      }
      
      this.isEncrypted = true;
      logger.info('Khởi tạo thông tin đăng nhập thành công');
    } catch (e) {
      logger.error(`Không thể khởi tạo thông tin đăng nhập: ${e}`);
      throw new Error(`Không thể khởi tạo thông tin đăng nhập: ${e}`);
    }
  }
  
  static getCredentials(type: 'valid' | 'invalid') {
    if (!this.credentials[type]) {
      throw new Error(`Chưa khởi tạo thông tin đăng nhập ${type}`);
    }
    
    return {
      username: this.decrypt(this.credentials[type].username),
      password: this.decrypt(this.credentials[type].password)
    };
  }
  
  static async login(page: Page, type: 'valid' | 'invalid' = 'valid') {
    const { username, password } = this.getCredentials(type);
    const baseUrl = getAppBaseUrl();
    
    await page.goto(`${baseUrl}/login`);
    await page.fill('input[type="email"]', username);
    await page.fill('input[type="password"]', password);
    await page.click('button[type="submit"]');
    
    logger.info(`Đã đăng nhập với thông tin ${type} vào ${baseUrl}`);
    return { username, password };
  }
}
