// utils/helpers.ts
import logger from './logger';

/**
 * <PERSON><PERSON>m retry cho các thao tác bất đồng bộ
 * @param fn Hàm cần retry
 * @param retries Số lần thử lại
 * @param delay Thời gian chờ gi<PERSON>a các lần thử (ms)
 */
export async function retry<T>(
  fn: () => Promise<T>,
  retries: number = 3,
  delay: number = 1000
): Promise<T> {
  try {
    return await fn();
  } catch (error) {
    if (retries === 0) {
      logger.error(`Retry failed: ${error}`);
      throw error;
    }
    logger.warn(`Retrying... ${retries} attempts left`);
    await new Promise((resolve) => setTimeout(resolve, delay));
    return retry(fn, retries - 1, delay);
  }
}