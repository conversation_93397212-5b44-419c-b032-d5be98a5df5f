import { SecretsManagerClient, GetSecretValueCommand } from "@aws-sdk/client-secrets-manager";

export async function getSecret(secretName: string): Promise<string> {
  const client = new SecretsManagerClient({ region: "ap-southeast-2" });
  
  try {
    const response = await client.send(
      new GetSecretValueCommand({ SecretId: secretName })
    );
    return response.SecretString || '';
  } catch (error) {
    console.error(`Error retrieving secret ${secretName}:`, error);
    return '';
  }
}