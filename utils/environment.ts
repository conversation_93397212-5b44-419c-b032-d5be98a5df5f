import logger from './logger';

export enum AppEnvironment {
  DEV = 'dev',
  PRODUCTION = 'production'
}

export enum RunEnvironment {
  DEVELOPMENT = 'development',
  PRODUCTION = 'production',
  CI = 'ci'
}

export function getEnvironments() {
  // Xác định môi trường chạy code
  const runEnv = process.env.CI 
    ? RunEnvironment.CI 
    : (process.env.NODE_ENV === 'production' ? RunEnvironment.PRODUCTION : RunEnvironment.DEVELOPMENT);
  
  // Xác định môi trường của ứng dụng đang test
  const appEnv = process.env.APP_ENV === 'production' ? AppEnvironment.PRODUCTION : AppEnvironment.DEV;
  
  logger.info(`Running in ${runEnv} environment, testing ${appEnv} application`);
  
  return {
    runEnv,
    appEnv,
    isRunningInProduction: runEnv === RunEnvironment.PRODUCTION || runEnv === RunEnvironment.CI,
    isTestingProduction: appEnv === AppEnvironment.PRODUCTION
  };
}

export function getAppBaseUrl() {
  const { appEnv } = getEnvironments();
  
  return appEnv === AppEnvironment.PRODUCTION
    ? 'https://portal.herond.org' // URL production (giả định)
    : 'https://portal-v2-dev.herond.org';
}
