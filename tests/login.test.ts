import { test, expect } from '@playwright/test';
import { LoginPage } from '../pages/LoginPage';
import { HomePage } from '../pages/HomePage';
import { DailyTasksPage } from '../pages/DailyTasksPage';
import { AuthManager } from '../utils/auth';
import * as cred from './test-data/user-data/credentials.json';
import logger from '../utils/logger';

test.beforeAll(async () => {
  await AuthManager.initialize();
});

test.describe('Login Tests', () => {
  test('Successful login', async ({ page }) => {
    const loginPage = new LoginPage(page);
    await loginPage.navigate();
    const homePage = new HomePage(page);
    
    const creds = AuthManager.getCredentials('valid');
    await loginPage.enterUsername(creds.username);
    await loginPage.enterPassword(creds.password);
    await loginPage.clickLogin();
    
    // <PERSON><PERSON>i chuyển hướng sau khi đăng nhập thành công
    await homePage.verifyHomeLogoIsVisible();
    
    logger.info('Login test completed');
    expect(page.url()).toContain('/point/daily-tasks');
  });

  test('Failed login with wrong credentials', async ({ page }) => {
    const loginPage = new LoginPage(page);
    await loginPage.navigate();
    
    const creds = AuthManager.getCredentials('invalid');
    await loginPage.enterUsername(creds.username);
    await loginPage.enterPassword(creds.password);
    await loginPage.clickLogin();
    
    const errorMessage = await loginPage.getErrorMessage();
    expect(errorMessage).toContain('Invalid email or password!');
    logger.info('Failed login test completed');
  });
});
